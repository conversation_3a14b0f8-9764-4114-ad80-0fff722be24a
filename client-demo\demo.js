// Demo Flow Controller
class OrderProcessingDemo {
    constructor() {
        this.currentStep = 0;
        this.isRunning = false;
        this.totalDuration = 15000; // 15 seconds total
        this.steps = [
            {
                id: 'step1',
                duration: 2000,
                status: '📧 Detecting email...',
                completedStatus: '✅ Email detected',
                progressText: 'Email received and queued for processing',
                statusText: 'Processing'
            },
            {
                id: 'step2',
                duration: 3000,
                status: '🤖 AI analyzing...',
                completedStatus: '✅ Analysis complete',
                progressText: 'AI performing intelligent classification',
                statusText: 'Analyzing'
            },
            {
                id: 'step3',
                duration: 4000,
                status: '📄 Extracting data...',
                completedStatus: '✅ Data extracted',
                progressText: 'AI reading PDF and extracting order details',
                statusText: 'Extracting'
            },
            {
                id: 'step4',
                duration: 3000,
                status: '🔍 Validating...',
                completedStatus: '✅ Validation passed',
                progressText: 'Smart validation of pricing and inventory',
                statusText: 'Validating'
            },
            {
                id: 'step5',
                duration: 3000,
                status: '✅ Creating order...',
                completedStatus: '✅ Order created',
                progressText: 'Order created and customer notified',
                statusText: 'Completing'
            }
        ];

        this.progressBar = null;
        this.progressStatus = null;
        this.progressTime = null;
        this.orderCounter = null;

        this.init();
    }

    init() {
        // Get DOM elements
        this.startBtn = document.getElementById('startDemo');
        this.resetBtn = document.getElementById('resetDemo');
        this.resultsSection = document.getElementById('results');
        this.progressFill = document.getElementById('progressFill');
        this.progressStatus = document.getElementById('progressStatus');
        this.progressTime = document.getElementById('progressTime');
        this.orderCounter = document.getElementById('orderCounter');

        // Bind event listeners
        this.startBtn.addEventListener('click', () => this.startDemo());
        this.resetBtn.addEventListener('click', () => this.resetDemo());

        // Initialize demo state
        this.resetDemo();

        // Start order counter animation
        this.animateOrderCounter();
    }

    animateOrderCounter() {
        // Randomly increment the order counter to simulate live processing
        setInterval(() => {
            if (this.orderCounter) {
                const currentValue = parseInt(this.orderCounter.textContent.replace(/,/g, ''));
                const newValue = currentValue + Math.floor(Math.random() * 3) + 1;
                this.orderCounter.textContent = newValue.toLocaleString();

                // Add a brief highlight effect
                this.orderCounter.classList.add('counter-highlight');
                setTimeout(() => {
                    this.orderCounter.classList.remove('counter-highlight');
                }, 500);
            }
        }, 5000);
    }

    startDemo() {
        if (this.isRunning) return;

        this.isRunning = true;
        this.startBtn.disabled = true;
        this.startBtn.querySelector('.btn-text').textContent = 'Processing...';
        this.startBtn.querySelector('.btn-icon').textContent = '⏳';

        // Hide results
        this.resultsSection.classList.remove('show');

        // Reset progress bar
        this.progressFill.style.width = '0%';
        this.progressStatus.textContent = 'Starting process...';
        this.progressTime.textContent = 'Total time: 0 / 15 seconds';

        // Start progress bar animation
        this.startProgressBar();

        // Start processing steps
        this.processStep(0);
    }

    startProgressBar() {
        let elapsedTime = 0;
        const updateInterval = 100; // Update every 100ms

        this.progressInterval = setInterval(() => {
            elapsedTime += updateInterval;
            const progressPercent = Math.min((elapsedTime / this.totalDuration) * 100, 100);

            // Update progress bar
            this.progressFill.style.width = `${progressPercent}%`;

            // Update time display
            const secondsElapsed = (elapsedTime / 1000).toFixed(1);
            this.progressTime.textContent = `Total time: ${secondsElapsed} / 15 seconds`;

            if (elapsedTime >= this.totalDuration) {
                clearInterval(this.progressInterval);
            }
        }, updateInterval);
    }

    processStep(stepIndex) {
        if (stepIndex >= this.steps.length) {
            this.completeDemo();
            return;
        }

        const step = this.steps[stepIndex];
        const stepElement = document.getElementById(step.id);
        const statusIndicator = stepElement.querySelector('.status-indicator');
        const statusText = stepElement.querySelector('.status-text');

        // Update progress status
        this.progressStatus.textContent = step.progressText;

        // Activate current step
        stepElement.classList.add('active');
        statusIndicator.textContent = '⚙️';
        statusText.textContent = step.statusText;

        // Add processing animation
        this.addProcessingAnimation(stepElement);

        // Complete step after duration
        setTimeout(() => {
            this.completeStep(stepIndex);
            this.processStep(stepIndex + 1);
        }, step.duration);
    }

    completeStep(stepIndex) {
        const step = this.steps[stepIndex];
        const stepElement = document.getElementById(step.id);
        const statusIndicator = stepElement.querySelector('.status-indicator');
        const statusText = stepElement.querySelector('.status-text');

        // Mark as completed
        stepElement.classList.remove('active');
        stepElement.classList.add('completed');
        statusIndicator.textContent = '✅';
        statusText.textContent = 'Completed';

        // Remove processing animation
        this.removeProcessingAnimation(stepElement);

        // Add completion effect
        this.addCompletionEffect(stepElement);
    }

    completeDemo() {
        // Clear progress interval
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Final progress updates
        this.progressFill.style.width = '100%';
        this.progressStatus.textContent = 'Order processing complete! 🎉';
        this.progressTime.textContent = 'Total time: 15.0 / 15 seconds';

        // Show results
        this.resultsSection.classList.add('show');

        // Reset button state
        this.startBtn.disabled = false;
        this.startBtn.querySelector('.btn-text').textContent = 'Start Live Demo';
        this.startBtn.querySelector('.btn-icon').textContent = '▶';
        this.isRunning = false;

        // Add celebration effect
        this.addCelebrationEffect();

        // Increment order counter
        if (this.orderCounter) {
            const currentValue = parseInt(this.orderCounter.textContent.replace(/,/g, ''));
            this.orderCounter.textContent = (currentValue + 1).toLocaleString();
        }
    }

    resetDemo() {
        // Clear any running intervals
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Reset all steps
        this.steps.forEach((step, index) => {
            const stepElement = document.getElementById(step.id);
            const statusIndicator = stepElement.querySelector('.status-indicator');
            const statusText = stepElement.querySelector('.status-text');

            stepElement.classList.remove('active', 'completed');
            statusIndicator.textContent = '⏳';
            statusText.textContent = 'Waiting';
            this.removeProcessingAnimation(stepElement);
        });

        // Reset progress bar
        this.progressFill.style.width = '0%';
        this.progressStatus.textContent = 'Ready to start';
        this.progressTime.textContent = 'Total time: 15 seconds';

        // Hide results
        this.resultsSection.classList.remove('show');

        // Reset button state
        this.startBtn.disabled = false;
        this.startBtn.querySelector('.btn-text').textContent = 'Start Live Demo';
        this.startBtn.querySelector('.btn-icon').textContent = '▶';
        this.isRunning = false;
        this.currentStep = 0;
    }

    addProcessingAnimation(element) {
        // Add pulsing animation to the step
        element.style.animation = 'stepPulse 1.5s ease-in-out infinite';

        // Add spinning animation to step icon
        const icon = element.querySelector('.step-icon');
        if (icon) {
            icon.style.animation = 'iconSpin 2s linear infinite';
        }

        // Add pulse animation to step pulse element
        const pulse = element.querySelector('.step-pulse');
        if (pulse) {
            pulse.style.animation = 'pulseRing 2s ease-out infinite';
        }

        // Add thinking dots animation if present
        const thinkingDots = element.querySelector('.thinking-dots');
        if (thinkingDots) {
            thinkingDots.style.animation = 'thinkingDots 1.5s ease-in-out infinite';
        }
    }

    removeProcessingAnimation(element) {
        element.style.animation = '';

        const icon = element.querySelector('.step-icon');
        if (icon) {
            icon.style.animation = '';
        }

        const pulse = element.querySelector('.step-pulse');
        if (pulse) {
            pulse.style.animation = '';
        }

        const thinkingDots = element.querySelector('.thinking-dots');
        if (thinkingDots) {
            thinkingDots.style.animation = '';
        }
    }

    addCompletionEffect(element) {
        // Add completion glow effect
        element.style.boxShadow = '0 0 30px rgba(72, 187, 120, 0.6)';
        element.style.transform = 'scale(1.02)';

        // Add success particle effect
        this.createSuccessParticles(element);

        setTimeout(() => {
            element.style.boxShadow = '';
            element.style.transform = '';
        }, 1500);
    }

    createSuccessParticles(element) {
        const rect = element.getBoundingClientRect();
        const particles = ['✨', '⭐', '💫', '🌟'];

        for (let i = 0; i < 6; i++) {
            const particle = document.createElement('div');
            particle.textContent = particles[Math.floor(Math.random() * particles.length)];
            particle.style.cssText = `
                position: fixed;
                left: ${rect.left + rect.width / 2}px;
                top: ${rect.top + rect.height / 2}px;
                font-size: 20px;
                pointer-events: none;
                z-index: 1000;
                animation: particleFloat 2s ease-out forwards;
                animation-delay: ${i * 0.1}s;
            `;

            document.body.appendChild(particle);

            setTimeout(() => {
                if (document.body.contains(particle)) {
                    document.body.removeChild(particle);
                }
            }, 2000);
        }
    }

    addCelebrationEffect() {
        // Create multiple celebration elements
        const celebrations = ['🎉', '🎊', '✨', '🚀', '💫'];

        celebrations.forEach((emoji, index) => {
            const celebration = document.createElement('div');
            celebration.textContent = emoji;
            celebration.style.cssText = `
                position: fixed;
                top: ${20 + index * 15}%;
                left: ${20 + index * 15}%;
                font-size: ${60 + index * 10}px;
                z-index: 1000;
                animation: celebrate 3s ease-out forwards;
                animation-delay: ${index * 0.2}s;
                pointer-events: none;
            `;

            document.body.appendChild(celebration);

            setTimeout(() => {
                if (document.body.contains(celebration)) {
                    document.body.removeChild(celebration);
                }
            }, 3000);
        });

        // Add screen flash effect
        const flash = document.createElement('div');
        flash.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(72, 187, 120, 0.1);
            z-index: 999;
            animation: flashEffect 0.5s ease-out;
            pointer-events: none;
        `;

        document.body.appendChild(flash);

        setTimeout(() => {
            if (document.body.contains(flash)) {
                document.body.removeChild(flash);
            }
        }, 500);
    }
}

// Enhanced animations and effects
function addCustomStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* Enhanced Animations */
        @keyframes stepPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.02); opacity: 0.95; }
        }

        @keyframes iconSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulseRing {
            0% { transform: scale(0.8); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
        }

        @keyframes thinkingDots {
            0%, 20% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        @keyframes particleFloat {
            0% { transform: translate(0, 0) scale(1); opacity: 1; }
            100% {
                transform: translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px) scale(0);
                opacity: 0;
            }
        }

        @keyframes celebrate {
            0% { transform: scale(0) rotate(0deg); opacity: 1; }
            50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
            100% { transform: scale(0.8) rotate(360deg); opacity: 0; }
        }

        @keyframes flashEffect {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        /* Enhanced Step Styles */
        .flow-step {
            position: relative;
            overflow: visible;
        }

        .step-number {
            position: absolute;
            left: -15px;
            top: 15px;
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 700;
            box-shadow: 0 4px 10px rgba(102, 126, 234, 0.3);
            z-index: 10;
        }

        .step-icon-container {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .step-pulse {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 2px solid #667eea;
            border-radius: 50%;
            opacity: 0;
        }

        .flow-step.active .step-pulse {
            opacity: 0.6;
        }

        .step-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .step-timing {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .step-description {
            font-size: 14px;
            color: #718096;
            margin-bottom: 15px;
        }

        .flow-step.active .step-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .flow-step.completed .step-icon {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }

        .flow-step.completed .step-number {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }

        /* Progress Bar Enhancements */
        .progress-container {
            margin-bottom: 40px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #48bb78);
            border-radius: 4px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShimmer 2s infinite;
        }

        @keyframes progressShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #4a5568;
        }

        /* Button Enhancements */
        .demo-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .demo-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-icon {
            font-size: 16px;
        }

        .btn-text {
            font-weight: 600;
        }

        /* Counter Highlight */
        .counter-highlight {
            animation: counterPulse 0.5s ease-out;
        }

        @keyframes counterPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); color: #48bb78; }
            100% { transform: scale(1); }
        }

        /* Step Details Enhancement */
        .step-details {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease-out, opacity 0.3s ease-out;
            opacity: 0;
        }

        .flow-step.active .step-details,
        .flow-step.completed .step-details {
            max-height: 400px;
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
}

// Smooth scrolling for better UX
function addSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Auto-scroll to active step
function scrollToActiveStep() {
    const activeStep = document.querySelector('.flow-step.active');
    if (activeStep) {
        activeStep.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }
}

// Initialize demo when page loads
document.addEventListener('DOMContentLoaded', function () {
    // Add custom styles
    addCustomStyles();

    // Add smooth scrolling
    addSmoothScrolling();

    // Initialize the demo
    const demo = new OrderProcessingDemo();

    // Add scroll-to-step functionality
    const originalProcessStep = demo.processStep.bind(demo);
    demo.processStep = function (stepIndex) {
        originalProcessStep(stepIndex);
        setTimeout(scrollToActiveStep, 100);
    };

    // Add keyboard shortcuts
    document.addEventListener('keydown', function (e) {
        if (e.key === ' ' || e.key === 'Enter') {
            e.preventDefault();
            if (!demo.isRunning) {
                demo.startDemo();
            }
        } else if (e.key === 'Escape' || e.key === 'r') {
            demo.resetDemo();
        }
    });

    // Add touch/mobile support
    let touchStartY = 0;
    document.addEventListener('touchstart', function (e) {
        touchStartY = e.touches[0].clientY;
    });

    document.addEventListener('touchend', function (e) {
        const touchEndY = e.changedTouches[0].clientY;
        const diff = touchStartY - touchEndY;

        // Swipe up to start demo
        if (diff > 50 && !demo.isRunning) {
            demo.startDemo();
        }
        // Swipe down to reset
        else if (diff < -50) {
            demo.resetDemo();
        }
    });

    console.log('🚀 Agentic AI Demo loaded successfully!');
    console.log('💡 Keyboard shortcuts: Space/Enter = Start, Escape/R = Reset');
    console.log('📱 Mobile: Swipe up = Start, Swipe down = Reset');
});

// Export for potential external use
window.OrderProcessingDemo = OrderProcessingDemo;
